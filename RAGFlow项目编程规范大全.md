# RAGFlow项目编程规范大全

## 📋 概述

本文档基于RAGFlow项目代码库的深度分析，总结了项目中已遵循的编程规范和最佳实践，同时识别了需要改进的地方。文档涵盖Python后端代码（102个文件，29,698行）和TypeScript/React前端代码（642个文件，约25,000-30,000行）。

## 🐍 Python编程规范

### 1. 命名约定

#### ✅ 良好实践
```python
# 文件命名：使用下划线分隔的小写字母
chat_model.py
task_executor.py
redis_conn.py

# 类名：使用PascalCase
class Base(ABC):
class DocumentService:
class KnowledgebaseService:

# 函数名：使用snake_case
def chat_streamly(self, system, history, gen_conf):
def server_error_response(e):
def construct_json_result(code, message, data=None):

# 常量：使用大写字母和下划线
LENGTH_NOTIFICATION_CN = "······\n由于大模型的上下文窗口大小限制，回答已经被大模型截断。"
MAXIMUM_OF_UPLOADING_FILES = 256
BATCH_SIZE = 64
```

#### ⚠️ 需要改进
```python
# 避免使用缩写和不清晰的命名
# 不好的例子
def __clean(self, line):  # 应该是 clean_line 或 sanitize_line
pn = 0  # 应该是 page_number
tol = self.total_token_count(resp)  # 应该是 total_tokens

# 改进建议
def clean_line(self, line):
page_number = 0
total_tokens = self.total_token_count(resp)
```

### 2. 代码结构和组织

#### ✅ 良好实践
```python
# 标准的文件头部版权声明
#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  ...

# 清晰的导入分组
import re
import os
import json
from abc import ABC
from openai import OpenAI
from rag.nlp import is_chinese, is_english
from rag.utils import num_tokens_from_string

# 工厂模式的使用
FACTORY = {
    "general": naive,
    ParserType.NAIVE.value: naive,
    ParserType.PAPER.value: paper,
    ParserType.BOOK.value: book,
    # ...
}
```

#### ⚠️ 需要改进
```python
# 大文件问题：chat_model.py (1,587行) 应该拆分
# 建议拆分为：
# - base_chat_model.py (基础类)
# - openai_chat_model.py (OpenAI相关)
# - azure_chat_model.py (Azure相关)
# - anthropic_chat_model.py (Anthropic相关)
# - local_chat_model.py (本地模型)

# 过长的函数应该拆分
def very_long_function():  # 超过50行的函数
    # 应该拆分为多个小函数
    pass
```

### 3. 错误处理规范

#### ✅ 良好实践
```python
# 统一的错误处理模式
def server_error_response(e):
    logging.exception(e)
    try:
        if e.code == 401:
            return get_json_result(code=401, message=repr(e))
    except BaseException:
        pass
    return get_json_result(code=settings.RetCode.EXCEPTION_ERROR, message=repr(e))

# API错误处理
try:
    response = self.client.chat.completions.create(
        model=self.model_name,
        messages=history,
        **gen_conf)
    return ans, self.total_token_count(response)
except openai.APIError as e:
    return "**ERROR**: " + str(e), 0
```

#### ⚠️ 需要改进
```python
# 避免裸露的except子句
try:
    # some code
except:  # 不好：捕获所有异常
    pass

# 改进：具体化异常类型
try:
    # some code
except (ValueError, TypeError) as e:
    logging.error(f"Specific error occurred: {e}")
    raise
```

### 4. 日志记录规范

#### ✅ 良好实践
```python
# 统一的日志初始化
def initRootLogger(logfile_basename: str, log_format: str = "%(asctime)-15s %(levelname)-8s %(process)d %(message)s"):
    logger = logging.getLogger()
    log_path = os.path.abspath(os.path.join(get_project_base_directory(), "logs", f"{logfile_basename}.log"))
    
    formatter = logging.Formatter(log_format)
    handler1 = RotatingFileHandler(log_path, maxBytes=10*1024*1024, backupCount=5)
    handler1.setFormatter(formatter)
    logger.addHandler(handler1)

# 异常日志记录
logging.exception(e)  # 自动包含堆栈跟踪
```

### 5. 类型注解规范

#### ✅ 良好实践
```python
# 使用Pydantic进行数据验证
class Chunk(BaseModel):
    id: str = ""
    content: str = ""
    document_id: str = ""
    important_keywords: list = Field(default_factory=list)
    positions: list[list[int]] = Field(default_factory=list)
    
    @validator('positions')
    def validate_positions(cls, value):
        for sublist in value:
            if len(sublist) != 5:
                raise ValueError("Each sublist in positions must have a length of 5")
        return value

# 函数类型注解
ErrorHandlerFn = Callable[[BaseException | None, str | None, dict | None], None]
```

#### ⚠️ 需要改进
```python
# 缺少类型注解的函数
def chat(self, system, history, gen_conf):  # 应该添加类型注解
    pass

# 改进建议
def chat(self, system: str, history: List[Dict[str, str]], gen_conf: Dict[str, Any]) -> Tuple[str, int]:
    pass
```

## ⚛️ TypeScript/React编程规范

### 1. 组件命名和文件组织

#### ✅ 良好实践
```typescript
// 文件命名：kebab-case
file-upload-modal/
message-item/
pdf-previewer/

// 组件命名：PascalCase
export const ChatDrawer: React.FC = () => {};
export const FormSheet: React.FC = () => {};
export const RagNode: React.FC = () => {};

// Hook命名：use前缀 + camelCase
export const useSelectCanvasData = () => {};
export const useFetchFlow = () => {};
export const useHandleDrop = () => {};
```

#### ⚠️ 需要改进
```typescript
// 避免过深的目录嵌套
// 当前：web/src/pages/agent/canvas/node/categorize-node/
// 建议：web/src/pages/agent/nodes/CategorizeNode/

// 统一文件扩展名
// 混用 .ts 和 .tsx，建议：
// - 纯逻辑文件使用 .ts
// - 包含JSX的文件使用 .tsx
```

### 2. TypeScript类型定义

#### ✅ 良好实践
```typescript
// 接口定义清晰
export interface IFlow {
  avatar?: null | string;
  canvas_type: null;
  create_date: string;
  create_time: number;
  description: null;
  dsl: DSL;
  id: string;
  title: string;
  update_date: string;
  update_time: number;
  user_id: string;
}

// 泛型使用
declare global {
  type Nullable<T> = T | null;
}

// 严格的类型检查
@validator('positions')
def validate_positions(cls, value):
    for sublist in value:
        if len(sublist) != 5:
            raise ValueError("Each sublist in positions must have a length of 5")
    return value
```

### 3. React Hooks使用规范

#### ✅ 良好实践
```typescript
// 自定义Hook的良好实践
export const useFetchFlow = (): {
  data: IFlow;
  loading: boolean;
  refetch: () => void;
} => {
  const { id } = useParams();
  const { sharedId } = useGetSharedChatSearchParams();

  const {
    data,
    isFetching: loading,
    refetch,
  } = useQuery({
    queryKey: ['flowDetail'],
    initialData: {} as IFlow,
    refetchOnReconnect: false,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    gcTime: 0,
    queryFn: async () => {
      const { data } = await flowService.getCanvas({}, sharedId || id);
      return data?.data ?? {};
    },
  });

  return { data, loading, refetch };
};

// 回调引用优化
function useCallbackRef<T extends (...args: never[]) => unknown>(
  callback: T | undefined,
): T {
  const callbackRef = React.useRef(callback);

  React.useEffect(() => {
    callbackRef.current = callback;
  });

  return React.useMemo(
    () => ((...args) => callbackRef.current?.(...args)) as T,
    [],
  );
}
```

### 4. 状态管理规范

#### ✅ 良好实践
```typescript
// Zustand store的使用
const selector = (state: RFState) => ({
  nodes: state.nodes,
  edges: state.edges,
  onNodesChange: state.onNodesChange,
  onEdgesChange: state.onEdgesChange,
  onConnect: state.onConnect,
  setNodes: state.setNodes,
  onSelectionChange: state.onSelectionChange,
});

export const useSelectCanvasData = () => {
  return useGraphStore(selector);
};
```

### 5. 错误处理和请求管理

#### ✅ 良好实践
```typescript
// 统一的错误处理
const errorHandler = (error: {
  response: Response;
  message: string;
}): Response => {
  const { response } = error;
  if (error.message === FAILED_TO_FETCH) {
    notification.error({
      description: i18n.t('message.networkAnomalyDescription'),
      message: i18n.t('message.networkAnomaly'),
    });
  } else {
    if (response && response.status) {
      const errorText =
        RetcodeMessage[response.status as ResultCode] || response.statusText;
      const { status, url } = response;
      notification.error({
        message: `${i18n.t('message.requestError')} ${status}: ${url}`,
        description: errorText,
      });
    }
  }
  return response ?? { data: { code: 1999 } };
};

// 响应拦截器
request.interceptors.response.use(async (response: Response, options) => {
  if (response?.status === 413 || response?.status === 504) {
    message.error(RetcodeMessage[response?.status as ResultCode]);
  }

  const data: ResponseType = await response?.clone()?.json();
  if (data?.code === 100) {
    message.error(data?.message);
  } else if (data?.code === 401) {
    notification.error({
      message: data?.message,
      description: data?.message,
      duration: 3,
    });
    authorizationUtil.removeAll();
    redirectToLogin();
  }
  return response;
});
```

## 📁 项目结构规范

### 1. 目录组织原则

#### ✅ 良好实践
```
# 按功能模块组织
rag/
├── app/          # 文档解析器（按文档类型）
├── llm/          # 大语言模型（按模型类型）
├── nlp/          # 自然语言处理
├── utils/        # 工具模块（按连接类型）
└── svr/          # 服务器模块

api/
├── apps/         # 应用模块（按业务功能）
├── db/           # 数据库相关
├── utils/        # 工具函数
└── sdk/          # SDK模块

web/src/
├── pages/        # 页面组件（按功能模块）
├── components/   # 通用组件
├── hooks/        # 自定义Hooks
├── services/     # 服务层
├── utils/        # 工具函数
└── interfaces/   # 类型定义
```

### 2. 文件命名规范

#### ✅ 良好实践
```python
# Python文件：snake_case
task_executor.py
redis_conn.py
chat_model.py

# TypeScript文件：kebab-case
flow-hooks.ts
chat-service.ts
common-util.ts

# React组件：PascalCase目录 + index.tsx
components/
├── MessageItem/
│   └── index.tsx
├── FileUploadModal/
│   └── index.tsx
```

## 📊 代码质量标准

### 1. 文件大小限制

#### 🚨 当前问题
- `chat_model.py`: 1,587行 ❌ (建议 < 500行)
- `doc.py`: 1,407行 ❌ (建议 < 500行)
- `db_models.py`: 1,124行 ❌ (建议 < 500行)

#### ✅ 改进建议
```python
# 拆分大文件的策略
# chat_model.py -> 
#   ├── base_chat_model.py
#   ├── openai_models.py
#   ├── azure_models.py
#   ├── anthropic_models.py
#   └── local_models.py

# doc.py ->
#   ├── document_upload.py
#   ├── document_processing.py
#   ├── document_retrieval.py
#   └── document_management.py
```

### 2. 函数复杂度控制

#### ✅ 标准
- 单个函数不超过50行
- 圈复杂度不超过10
- 嵌套层级不超过4层

#### ⚠️ 重构建议
```python
# 过长函数的重构示例
def process_document(self, doc):  # 原函数100+行
    # 拆分为：
    def validate_document(self, doc):
        pass
    
    def extract_content(self, doc):
        pass
    
    def process_chunks(self, chunks):
        pass
    
    def save_results(self, results):
        pass
```

### 3. 注释和文档规范

#### ✅ 良好实践
```python
def upload(dataset_id, tenant_id):
    """
    Upload documents to a dataset.
    ---
    tags:
      - Documents
    security:
      - ApiKeyAuth: []
    parameters:
      - in: path
        name: dataset_id
        type: string
        required: true
        description: ID of the dataset.
    """
```

#### ⚠️ 需要改进
```python
# 缺少文档字符串的函数
def chat(self, system, history, gen_conf):  # 需要添加docstring
    pass

# 改进建议
def chat(self, system: str, history: List[Dict], gen_conf: Dict) -> Tuple[str, int]:
    """
    Generate chat response using the language model.
    
    Args:
        system: System prompt for the conversation
        history: List of previous messages
        gen_conf: Generation configuration parameters
        
    Returns:
        Tuple of (response_text, token_count)
        
    Raises:
        openai.APIError: When API call fails
    """
    pass
```

## ✅ 规范检查清单

### Python代码检查
- [ ] 文件大小 < 500行
- [ ] 函数长度 < 50行
- [ ] 使用类型注解
- [ ] 包含docstring
- [ ] 统一错误处理
- [ ] 日志记录规范
- [ ] 导入语句分组
- [ ] 命名符合snake_case

### TypeScript代码检查
- [ ] 组件文件 < 300行
- [ ] 使用严格类型定义
- [ ] Hook命名以use开头
- [ ] 组件使用PascalCase
- [ ] 文件使用kebab-case
- [ ] 错误边界处理
- [ ] 性能优化（useMemo, useCallback）

### 项目结构检查
- [ ] 目录按功能组织
- [ ] 文件命名一致性
- [ ] 依赖关系清晰
- [ ] 配置文件集中管理
- [ ] 测试文件对应
- [ ] 文档完整性

## 🔧 工具和自动化

### 推荐工具
```bash
# Python
black          # 代码格式化
flake8         # 代码检查
mypy           # 类型检查
pytest         # 测试框架

# TypeScript/React
prettier       # 代码格式化
eslint         # 代码检查
typescript     # 类型检查
jest           # 测试框架
```

### 配置示例
```json
// .eslintrc.js
{
  "extends": [
    "@umijs/eslint-config-umi",
    "plugin:@typescript-eslint/recommended"
  ],
  "rules": {
    "max-lines": ["error", 300],
    "max-lines-per-function": ["error", 50],
    "complexity": ["error", 10]
  }
}
```

## 🎯 具体改进建议

### 1. 大文件重构计划

#### chat_model.py (1,587行) 重构方案
```python
# 当前结构问题
class Base(ABC):           # 基础类
class OpenAI(Base):        # OpenAI实现
class AzureOpenAI(Base):   # Azure实现
class Anthropic(Base):     # Anthropic实现
# ... 15个不同的模型类在同一文件

# 建议重构为模块化结构
rag/llm/
├── base/
│   ├── __init__.py
│   ├── chat_model_base.py      # 抽象基类
│   └── model_factory.py        # 工厂模式
├── openai/
│   ├── __init__.py
│   ├── openai_chat.py          # OpenAI实现
│   └── azure_openai.py         # Azure OpenAI实现
├── anthropic/
│   ├── __init__.py
│   └── claude_chat.py          # Claude实现
├── local/
│   ├── __init__.py
│   ├── ollama_chat.py          # Ollama实现
│   └── xinference_chat.py      # Xinference实现
└── __init__.py                 # 统一导出接口
```

#### doc.py (1,407行) 重构方案
```python
# 当前问题：单文件包含所有文档操作
# 建议拆分为：
api/apps/sdk/document/
├── __init__.py
├── upload.py              # 文档上传逻辑
├── processing.py          # 文档处理逻辑
├── retrieval.py           # 文档检索逻辑
├── management.py          # 文档管理逻辑
├── chunking.py            # 分块处理逻辑
└── models.py              # 数据模型定义
```

### 2. 代码质量改进示例

#### 错误处理标准化
```python
# 当前不一致的错误处理
# 文件A：
try:
    result = some_operation()
except Exception as e:
    return "**ERROR**: " + str(e), 0

# 文件B：
try:
    result = some_operation()
except BaseException:
    pass

# 建议统一的错误处理模式
class RAGFlowError(Exception):
    """RAGFlow基础异常类"""
    def __init__(self, message: str, error_code: int = 500):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class ModelError(RAGFlowError):
    """模型相关错误"""
    pass

class DocumentError(RAGFlowError):
    """文档处理错误"""
    pass

# 统一的错误处理装饰器
def handle_errors(error_type: Type[RAGFlowError] = RAGFlowError):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.exception(f"Error in {func.__name__}: {e}")
                raise error_type(f"Operation failed: {str(e)}")
        return wrapper
    return decorator

# 使用示例
@handle_errors(ModelError)
def chat(self, system: str, history: List[Dict], gen_conf: Dict) -> Tuple[str, int]:
    """Generate chat response with proper error handling."""
    pass
```

#### 配置管理标准化
```python
# 当前问题：配置散落在各处
timeout = int(os.environ.get('LM_TIMEOUT_SECONDS', 600))
BATCH_SIZE = 64
MAXIMUM_OF_UPLOADING_FILES = 256

# 建议：集中配置管理
# config/settings.py
from pydantic import BaseSettings
from typing import Optional

class RAGFlowSettings(BaseSettings):
    # 模型配置
    llm_timeout_seconds: int = 600
    max_tokens: int = 4096
    temperature: float = 0.1

    # 文档处理配置
    max_upload_files: int = 256
    chunk_size: int = 512
    chunk_overlap: int = 50

    # 数据库配置
    database_url: str
    redis_url: str

    # 存储配置
    storage_type: str = "minio"
    minio_endpoint: Optional[str] = None

    class Config:
        env_file = ".env"
        case_sensitive = False

# 使用配置
settings = RAGFlowSettings()
```

### 3. 前端代码优化建议

#### 组件拆分示例
```typescript
// 当前问题：大组件文件
// 建议拆分策略

// 原始大组件 (300+ 行)
const FlowCanvas: React.FC = () => {
  // 大量逻辑混在一起
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  // ... 大量状态和逻辑

  return (
    <div>
      {/* 复杂的JSX结构 */}
    </div>
  );
};

// 重构后的模块化结构
// components/FlowCanvas/
├── index.tsx              # 主组件
├── hooks/
│   ├── useFlowData.ts     # 数据管理Hook
│   ├── useFlowEvents.ts   # 事件处理Hook
│   └── useFlowValidation.ts # 验证逻辑Hook
├── components/
│   ├── NodePanel.tsx      # 节点面板
│   ├── EdgePanel.tsx      # 连线面板
│   └── ToolBar.tsx        # 工具栏
└── types.ts               # 类型定义

// 主组件变得简洁
const FlowCanvas: React.FC = () => {
  const { nodes, edges, loading } = useFlowData();
  const { onNodesChange, onEdgesChange } = useFlowEvents();
  const { validateFlow } = useFlowValidation();

  return (
    <div className="flow-canvas">
      <ToolBar />
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
      />
      <NodePanel />
      <EdgePanel />
    </div>
  );
};
```

#### 类型安全改进
```typescript
// 当前问题：类型定义不够严格
interface IFlow {
  avatar?: null | string;  // 应该更具体
  canvas_type: null;       // 应该是联合类型
  dsl: DSL;               // DSL类型需要更详细定义
}

// 改进建议：更严格的类型定义
type AvatarUrl = string;
type CanvasType = 'workflow' | 'chat' | 'agent' | null;

interface FlowNode {
  id: string;
  type: NodeType;
  position: { x: number; y: number };
  data: NodeData;
}

interface FlowEdge {
  id: string;
  source: string;
  target: string;
  type?: EdgeType;
}

interface DSL {
  nodes: FlowNode[];
  edges: FlowEdge[];
  viewport: { x: number; y: number; zoom: number };
}

interface IFlow {
  id: string;
  title: string;
  description: string | null;
  avatar: AvatarUrl | null;
  canvas_type: CanvasType;
  dsl: DSL;
  create_date: string;
  update_date: string;
  create_time: number;
  update_time: number;
  user_id: string;
}

// 运行时类型验证
import { z } from 'zod';

const FlowSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1).max(100),
  description: z.string().nullable(),
  avatar: z.string().url().nullable(),
  canvas_type: z.enum(['workflow', 'chat', 'agent']).nullable(),
  dsl: z.object({
    nodes: z.array(z.object({
      id: z.string(),
      type: z.string(),
      position: z.object({ x: z.number(), y: z.number() }),
      data: z.record(z.any())
    })),
    edges: z.array(z.object({
      id: z.string(),
      source: z.string(),
      target: z.string()
    }))
  })
});

type Flow = z.infer<typeof FlowSchema>;
```

### 4. 性能优化建议

#### Python后端优化
```python
# 当前问题：同步处理大量文档
def process_documents(documents):
    results = []
    for doc in documents:
        result = process_single_document(doc)  # 同步处理
        results.append(result)
    return results

# 改进：异步批处理
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def process_documents_async(documents: List[Document]) -> List[ProcessResult]:
    """异步批处理文档"""
    semaphore = asyncio.Semaphore(10)  # 限制并发数

    async def process_with_semaphore(doc: Document) -> ProcessResult:
        async with semaphore:
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                return await loop.run_in_executor(
                    executor, process_single_document, doc
                )

    tasks = [process_with_semaphore(doc) for doc in documents]
    return await asyncio.gather(*tasks, return_exceptions=True)

# 缓存优化
from functools import lru_cache
import redis

class CacheManager:
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client

    def cached_embedding(self, text: str, model: str) -> List[float]:
        """缓存嵌入向量"""
        cache_key = f"embedding:{model}:{hash(text)}"
        cached = self.redis.get(cache_key)

        if cached:
            return json.loads(cached)

        embedding = generate_embedding(text, model)
        self.redis.setex(cache_key, 3600, json.dumps(embedding))
        return embedding
```

#### 前端性能优化
```typescript
// 当前问题：不必要的重渲染
const FlowNode: React.FC<Props> = ({ node, onUpdate }) => {
  // 每次父组件更新都会重新创建函数
  const handleClick = () => {
    onUpdate(node.id, { selected: true });
  };

  return <div onClick={handleClick}>{node.title}</div>;
};

// 改进：使用React.memo和useCallback
const FlowNode: React.FC<Props> = React.memo(({ node, onUpdate }) => {
  const handleClick = useCallback(() => {
    onUpdate(node.id, { selected: true });
  }, [node.id, onUpdate]);

  return <div onClick={handleClick}>{node.title}</div>;
});

// 虚拟化长列表
import { FixedSizeList as List } from 'react-window';

const VirtualizedNodeList: React.FC<{ nodes: FlowNode[] }> = ({ nodes }) => {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style}>
      <FlowNode node={nodes[index]} />
    </div>
  );

  return (
    <List
      height={600}
      itemCount={nodes.length}
      itemSize={50}
      width="100%"
    >
      {Row}
    </List>
  );
};

// 状态管理优化
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

interface FlowState {
  nodes: FlowNode[];
  edges: FlowEdge[];
  selectedNodeId: string | null;
  // 分离不同关注点的状态
  ui: {
    sidebarOpen: boolean;
    loading: boolean;
  };
}

const useFlowStore = create<FlowState>()(
  subscribeWithSelector((set, get) => ({
    nodes: [],
    edges: [],
    selectedNodeId: null,
    ui: {
      sidebarOpen: true,
      loading: false,
    },
    // 只更新需要的部分
    updateNode: (id: string, updates: Partial<FlowNode>) =>
      set((state) => ({
        nodes: state.nodes.map((node) =>
          node.id === id ? { ...node, ...updates } : node
        ),
      })),
  }))
);

// 选择器优化，避免不必要的重渲染
const useSelectedNode = () => useFlowStore(
  (state) => state.nodes.find(n => n.id === state.selectedNodeId),
  shallow
);
```

### 5. 测试规范

#### Python测试示例
```python
# tests/test_chat_model.py
import pytest
from unittest.mock import Mock, patch
from rag.llm.chat_model import OpenAI

class TestChatModel:
    @pytest.fixture
    def chat_model(self):
        return OpenAI(
            key="test-key",
            model_name="gpt-3.5-turbo",
            base_url="https://api.openai.com/v1"
        )

    @patch('openai.OpenAI')
    def test_chat_success(self, mock_openai, chat_model):
        # 模拟成功响应
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Test response"
        mock_response.choices[0].finish_reason = "stop"

        mock_openai.return_value.chat.completions.create.return_value = mock_response

        result, tokens = chat_model.chat(
            system="You are a helpful assistant",
            history=[{"role": "user", "content": "Hello"}],
            gen_conf={"temperature": 0.7}
        )

        assert result == "Test response"
        assert tokens >= 0

    @patch('openai.OpenAI')
    def test_chat_api_error(self, mock_openai, chat_model):
        # 模拟API错误
        mock_openai.return_value.chat.completions.create.side_effect = \
            openai.APIError("API Error")

        result, tokens = chat_model.chat(
            system="",
            history=[{"role": "user", "content": "Hello"}],
            gen_conf={}
        )

        assert result.startswith("**ERROR**:")
        assert tokens == 0
```

#### TypeScript测试示例
```typescript
// __tests__/FlowCanvas.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import FlowCanvas from '../FlowCanvas';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('FlowCanvas', () => {
  it('renders flow canvas with nodes', async () => {
    render(<FlowCanvas />, { wrapper: createWrapper() });

    expect(screen.getByTestId('flow-canvas')).toBeInTheDocument();

    // 等待数据加载
    await screen.findByTestId('flow-nodes');

    expect(screen.getByTestId('flow-nodes')).toBeInTheDocument();
  });

  it('handles node selection', async () => {
    const { user } = render(<FlowCanvas />, { wrapper: createWrapper() });

    const node = await screen.findByTestId('flow-node-1');
    await user.click(node);

    expect(node).toHaveClass('selected');
  });
});

// Hook测试
import { renderHook, act } from '@testing-library/react';
import { useFlowData } from '../hooks/useFlowData';

describe('useFlowData', () => {
  it('loads flow data correctly', async () => {
    const { result } = renderHook(() => useFlowData('flow-id'));

    expect(result.current.loading).toBe(true);

    await act(async () => {
      // 等待数据加载完成
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.nodes).toHaveLength(2);
  });
});
```

## 🚀 CI/CD和部署规范

### 1. 代码质量检查流水线

#### GitHub Actions配置示例
```yaml
# .github/workflows/code-quality.yml
name: Code Quality Check

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main ]

jobs:
  python-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        pip install black flake8 mypy pytest
        pip install -r requirements.txt

    - name: Code formatting check
      run: black --check --diff rag/ api/

    - name: Lint check
      run: flake8 rag/ api/ --max-line-length=100 --max-complexity=10

    - name: Type check
      run: mypy rag/ api/ --ignore-missing-imports

    - name: Run tests
      run: pytest tests/ -v --cov=rag --cov=api --cov-report=xml

    - name: Upload coverage
      uses: codecov/codecov-action@v3

  frontend-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: web/package-lock.json

    - name: Install dependencies
      run: cd web && npm ci

    - name: Lint check
      run: cd web && npm run lint

    - name: Type check
      run: cd web && npm run type-check

    - name: Run tests
      run: cd web && npm run test:coverage

    - name: Build check
      run: cd web && npm run build
```

### 2. 预提交钩子配置

#### pre-commit配置
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.9
        files: ^(rag|api)/.*\.py$

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=100, --max-complexity=10]
        files: ^(rag|api)/.*\.py$

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        files: ^(rag|api)/.*\.py$
        args: [--ignore-missing-imports]

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        files: ^web/src/.*\.(ts|tsx|js|jsx|json|css|md)$

  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.44.0
    hooks:
      - id: eslint
        files: ^web/src/.*\.(ts|tsx|js|jsx)$
        args: [--fix]
```

### 3. Docker化部署规范

#### 多阶段构建Dockerfile
```dockerfile
# Dockerfile.backend
FROM python:3.9-slim as base

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 开发阶段
FROM base as development
COPY requirements-dev.txt .
RUN pip install --no-cache-dir -r requirements-dev.txt
COPY . .
CMD ["python", "api/ragflow_server.py"]

# 生产阶段
FROM base as production
COPY rag/ ./rag/
COPY api/ ./api/
COPY conf/ ./conf/

# 创建非root用户
RUN useradd --create-home --shell /bin/bash ragflow
USER ragflow

EXPOSE 9380
CMD ["python", "api/ragflow_server.py"]

# 前端Dockerfile
FROM node:18-alpine as frontend-base

WORKDIR /app
COPY web/package*.json ./
RUN npm ci --only=production

FROM frontend-base as frontend-build
COPY web/ .
RUN npm run build

FROM nginx:alpine as frontend-production
COPY --from=frontend-build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  ragflow-server:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: production
    ports:
      - "9380:9380"
    environment:
      - DATABASE_URL=************************************/ragflow
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  task-executor:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: production
    command: ["python", "rag/svr/task_executor.py", "0"]
    environment:
      - DATABASE_URL=************************************/ragflow
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    deploy:
      replicas: 2

  web-server:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      target: frontend-production
    ports:
      - "3000:80"
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ragflow
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 4. 监控和日志规范

#### 结构化日志配置
```python
# utils/logging_config.py
import logging
import json
from datetime import datetime
from typing import Dict, Any

class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""

    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }

        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)

        # 添加自定义字段
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id

        return json.dumps(log_entry, ensure_ascii=False)

def setup_logging(service_name: str, log_level: str = "INFO"):
    """设置结构化日志"""
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(StructuredFormatter())
    logger.addHandler(console_handler)

    # 文件处理器
    file_handler = logging.FileHandler(f'logs/{service_name}.log')
    file_handler.setFormatter(StructuredFormatter())
    logger.addHandler(file_handler)

    return logger

# 使用示例
logger = setup_logging('ragflow-server')

def process_document(doc_id: str, user_id: str):
    logger.info(
        "Processing document",
        extra={'user_id': user_id, 'doc_id': doc_id}
    )
```

#### 性能监控
```python
# utils/metrics.py
import time
import functools
from typing import Callable, Any
from prometheus_client import Counter, Histogram, Gauge

# 定义指标
REQUEST_COUNT = Counter(
    'ragflow_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'ragflow_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

ACTIVE_CONNECTIONS = Gauge(
    'ragflow_active_connections',
    'Number of active connections'
)

def monitor_performance(func: Callable) -> Callable:
    """性能监控装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        start_time = time.time()

        try:
            result = func(*args, **kwargs)
            REQUEST_COUNT.labels(
                method=func.__name__,
                endpoint=func.__module__,
                status='success'
            ).inc()
            return result
        except Exception as e:
            REQUEST_COUNT.labels(
                method=func.__name__,
                endpoint=func.__module__,
                status='error'
            ).inc()
            raise
        finally:
            duration = time.time() - start_time
            REQUEST_DURATION.labels(
                method=func.__name__,
                endpoint=func.__module__
            ).observe(duration)

    return wrapper

# 使用示例
@monitor_performance
def chat(self, system: str, history: List[Dict], gen_conf: Dict):
    """带性能监控的聊天函数"""
    pass
```

### 5. 安全规范

#### 安全配置检查清单
```python
# security/security_config.py
import os
from typing import List, Dict

class SecurityConfig:
    """安全配置管理"""

    @staticmethod
    def validate_environment() -> List[str]:
        """验证环境变量安全性"""
        issues = []

        # 检查必需的安全环境变量
        required_vars = [
            'SECRET_KEY',
            'DATABASE_URL',
            'REDIS_URL',
            'JWT_SECRET'
        ]

        for var in required_vars:
            if not os.getenv(var):
                issues.append(f"Missing required environment variable: {var}")

        # 检查密钥强度
        secret_key = os.getenv('SECRET_KEY', '')
        if len(secret_key) < 32:
            issues.append("SECRET_KEY should be at least 32 characters long")

        return issues

    @staticmethod
    def get_cors_config() -> Dict[str, Any]:
        """CORS配置"""
        return {
            'origins': os.getenv('ALLOWED_ORIGINS', 'http://localhost:3000').split(','),
            'methods': ['GET', 'POST', 'PUT', 'DELETE'],
            'allow_headers': ['Content-Type', 'Authorization'],
            'expose_headers': ['X-Total-Count'],
            'supports_credentials': True,
            'max_age': 86400
        }

# 输入验证
from pydantic import BaseModel, validator
import re

class SecureDocumentUpload(BaseModel):
    filename: str
    content_type: str
    size: int

    @validator('filename')
    def validate_filename(cls, v):
        # 防止路径遍历攻击
        if '..' in v or '/' in v or '\\' in v:
            raise ValueError("Invalid filename")

        # 检查文件扩展名
        allowed_extensions = {'.pdf', '.txt', '.docx', '.md'}
        if not any(v.lower().endswith(ext) for ext in allowed_extensions):
            raise ValueError("File type not allowed")

        return v

    @validator('size')
    def validate_size(cls, v):
        max_size = 100 * 1024 * 1024  # 100MB
        if v > max_size:
            raise ValueError("File too large")
        return v

# API限流
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["1000 per hour", "100 per minute"]
)

@app.route('/api/upload')
@limiter.limit("10 per minute")
def upload_document():
    """限流的文档上传接口"""
    pass
```

## 📈 代码质量度量

### 1. 质量指标定义

#### 代码复杂度指标
```python
# 使用radon计算复杂度
# pip install radon

# 圈复杂度检查
radon cc rag/ api/ --min B  # B级以上复杂度

# 维护性指数
radon mi rag/ api/ --min B

# 代码行数统计
radon raw rag/ api/
```

#### 测试覆盖率目标
```bash
# Python测试覆盖率
pytest --cov=rag --cov=api --cov-report=html --cov-fail-under=80

# 前端测试覆盖率
cd web && npm run test:coverage -- --coverage-threshold=80
```

### 2. 代码审查清单

#### Pull Request检查项
- [ ] **功能性**
  - [ ] 代码实现符合需求
  - [ ] 边界条件处理完整
  - [ ] 错误处理适当

- [ ] **代码质量**
  - [ ] 命名清晰有意义
  - [ ] 函数长度 < 50行
  - [ ] 圈复杂度 < 10
  - [ ] 无重复代码

- [ ] **安全性**
  - [ ] 输入验证完整
  - [ ] 无SQL注入风险
  - [ ] 敏感信息不在代码中

- [ ] **性能**
  - [ ] 无明显性能问题
  - [ ] 数据库查询优化
  - [ ] 缓存使用合理

- [ ] **测试**
  - [ ] 单元测试覆盖率 > 80%
  - [ ] 集成测试通过
  - [ ] 手动测试验证

- [ ] **文档**
  - [ ] API文档更新
  - [ ] 代码注释充分
  - [ ] README更新

### 3. 持续改进计划

#### 技术债务管理
```python
# 技术债务跟踪
# TODO: 重构chat_model.py，拆分为多个模块 (优先级: 高, 预计工作量: 3天)
# FIXME: 修复doc.py中的内存泄漏问题 (优先级: 中, 预计工作量: 1天)
# HACK: 临时解决方案，需要在v2.0中重新设计 (优先级: 低, 预计工作量: 5天)

class TechnicalDebt:
    """技术债务跟踪"""

    def __init__(self):
        self.debts = []

    def add_debt(self, description: str, priority: str, estimated_effort: str, file_path: str):
        """添加技术债务项"""
        self.debts.append({
            'description': description,
            'priority': priority,
            'estimated_effort': estimated_effort,
            'file_path': file_path,
            'created_date': datetime.now(),
            'status': 'open'
        })

    def generate_report(self) -> str:
        """生成技术债务报告"""
        high_priority = [d for d in self.debts if d['priority'] == '高']
        return f"高优先级技术债务: {len(high_priority)}项"
```

#### 代码质量趋势监控
```yaml
# .github/workflows/quality-metrics.yml
name: Quality Metrics

on:
  schedule:
    - cron: '0 2 * * *'  # 每天凌晨2点运行

jobs:
  collect-metrics:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0  # 获取完整历史

    - name: Calculate metrics
      run: |
        # 代码行数趋势
        echo "Lines of code: $(find rag api -name '*.py' | xargs wc -l | tail -1)"

        # 复杂度趋势
        radon cc rag/ api/ --json > complexity.json

        # 测试覆盖率趋势
        pytest --cov=rag --cov=api --cov-report=json

    - name: Upload metrics
      # 上传到监控系统
      run: |
        curl -X POST "$METRICS_ENDPOINT" \
          -H "Content-Type: application/json" \
          -d @complexity.json
```

---

## 📚 参考资源

### 官方文档
- [Python PEP 8 Style Guide](https://pep8.org/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React Best Practices](https://react.dev/learn)

### 工具链
- **Python**: black, flake8, mypy, pytest, bandit
- **TypeScript**: prettier, eslint, typescript, jest
- **CI/CD**: GitHub Actions, pre-commit
- **监控**: Prometheus, Grafana, Sentry

### 代码质量标准
- **文件大小**: Python < 500行, TypeScript < 300行
- **函数复杂度**: 圈复杂度 < 10, 函数长度 < 50行
- **测试覆盖率**: 单元测试 > 80%, 集成测试 > 60%
- **文档覆盖率**: 公共API 100%, 内部函数 > 70%

---

*本规范文档基于RAGFlow项目v0.17.1的代码分析，建议定期更新以反映项目演进。最后更新时间：2024年12月*
