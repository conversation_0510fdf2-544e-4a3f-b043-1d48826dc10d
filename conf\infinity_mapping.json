{"id": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "doc_id": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "kb_id": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "create_time": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "create_timestamp_flt": {"type": "float", "default": 0.0}, "img_id": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "docnm_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "title_tks": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "title_sm_tks": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "name_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "important_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace-#"}, "tag_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace-#"}, "important_tks": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "question_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace-#"}, "question_tks": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "content_with_weight": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "content_ltks": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "content_sm_ltks": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "authors_tks": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "authors_sm_tks": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "page_num_int": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "top_int": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "position_int": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "weight_int": {"type": "integer", "default": 0}, "weight_flt": {"type": "float", "default": 0.0}, "rank_int": {"type": "integer", "default": 0}, "rank_flt": {"type": "float", "default": 0}, "available_int": {"type": "integer", "default": 1}, "knowledge_graph_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "entities_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace-#"}, "pagerank_fea": {"type": "integer", "default": 0}, "tag_feas": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "from_entity_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "to_entity_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "entity_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "entity_type_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}, "source_id": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "n_hop_with_weight": {"type": "<PERSON><PERSON><PERSON>", "default": ""}, "removed_kwd": {"type": "<PERSON><PERSON><PERSON>", "default": "", "analyzer": "whitespace"}}